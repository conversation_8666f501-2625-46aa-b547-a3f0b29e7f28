# Project Checklist

Ниже приведён список ключевых задач для завершения проекта «Online Store».

- [x] Добавить **README.md** с описанием проекта, архитектуры и инструкциями по запуску
- [x] Создать **.env.example** с перечнем обязательных переменных окружения: `REACT_APP_SUPABASE_URL`, `REACT_APP_SUPABASE_ANON_KEY`, `REACT_APP_SUPABASE_SERVICE_ROLE_KEY`, `PORT`, `API_PORT` и т.д.
- [x] Ввести **.gitignore** для исключения `node_modules/`, `build/`, `.env`, `.DS_Store` и др.
- [x] Настроить **CI** (GitHub Actions или GitLab CI) для запуска сборки, линтинга, тестов и аудита (например, Lighthouse)
- [x] Перенести тесты в единый фреймворк (Jest + React Testing Library) и покрыть ключевые компоненты, страницы и API
- [x] Интегрировать **ESLint + Prettier** в CI; настроить pre-commit хуки (Husky)
- [ ] Централизовать управление переменными окружения в рантайме с валидацией на старте
- [x] Проверить и довести до ума **SSR-скрипты**: `npm run ssr`, `npm run build:ssr`, `npm run build:full`
- [x] Убрать или обновить устаревшие скрипты и статические отчёты (например, `lighthouse-report.html`)
- [ ] Добавить **LICENSE** (если проект будет публично распространяться) 