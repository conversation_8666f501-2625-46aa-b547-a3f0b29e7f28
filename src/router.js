import React, { lazy, Suspense, useEffect } from 'react'; // Added React, lazy, Suspense, useEffect
import { createBrowserRouter, Navigate, useLocation } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import PrivateRoute from './components/PrivateRoute';
import LoadingSpinner from './components/LoadingSpinner'; // Added
import App from './App';
import Home from './pages/Home';
import CartPage from './pages/CartPage';
import WishlistPage from './pages/WishlistPage';
import ComparePage from './pages/ComparePage';
import CategoryPage from './pages/CategoryPage';
import ProductPage from './pages/ProductPage';
import ErrorPage from './pages/ErrorPage';
import ContactPage from './pages/ContactPage';
import SearchPage from './pages/Search';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage'; // Added
import ResetPasswordPage from './pages/ResetPasswordPage'; // Added
import AboutPage from './pages/AboutPage';
import SeedPage from './pages/SeedPage';
import AccountPage from './pages/AccountPage';
import AdminDashboard from './pages/AdminDashboard';
import AdminDashboardContent from './pages/admin/AdminDashboardContent'; // Added
import ProductManagement from './pages/admin/ProductManagement'; // Updated path
import NewProduct from './pages/admin/NewProduct'; // Added
import ProductEditPage from './pages/admin/ProductEditPage'; // Added
import AllProductsPage from './pages/AllProductsPage'; // Added import for AllProductsPage

import OrderConfirmationPage from './pages/OrderConfirmationPage';
import CheckoutPage from './pages/CheckoutPage';
import StoreManagement from './pages/admin/StoreManagement';
import StoreSettingsPage from './pages/admin/StoreSettingsPage';
import ProductParametersPage from './pages/admin/ProductParametersPage';
import AnalyticsPage from './pages/admin/AnalyticsPage';
import ReviewsManagementPage from './pages/admin/ReviewsManagementPage';
import OrdersManagement from './pages/admin/OrdersManagement';
import OrderDetailPage from './pages/admin/OrderDetailPage';
import AuthCallbackHandler from './components/AuthCallbackHandler';
import SalePage from './pages/SalePage';
import NewArrivalsPage from './pages/NewArrivalsPage';
import DiagnosticPage from './pages/DiagnosticPage';
import BestsellersPage from './pages/BestsellersPage'; // Added
import BrandManagement from './pages/admin/BrandManagement';
import BrandCreate from './pages/admin/BrandCreate';
import BrandEdit from './pages/admin/BrandEdit';
import OrdersPage from './pages/OrdersPage';
import UserOrderDetailPage from './pages/UserOrderDetailPage'; // Added import
import PartnersDemo from './pages/PartnersDemo'; // Added import for Partners Demo
// ProductEdit is already imported
// StoreManagement is already imported (from components/admin)
// OrdersManagement, OrderDetailPage, ProductParametersPage, AnalyticsPage, ReviewsManagementPage, ProductParametersPage are already imported.

const CategoryManagement = lazy(() => import('./pages/admin/CategoryManagement'));
const CategoryCreatePage = lazy(() => import('./pages/admin/CategoryCreatePage'));
const CategoryEditPage = lazy(() => import('./pages/admin/CategoryEditPage'));
// Temporarily point to the version in src/admin/pages for evaluation - NOW MOVED
const BannerManagement = lazy(() => import('./pages/admin/BannerManagement')); // Updated path
const BannerEditPage = lazy(() => import('./pages/admin/BannerEdit')); // Updated path
const BannerCreatePage = lazy(() => import('./pages/admin/BannerCreate')); // Updated path
const UsersManagement = lazy(() => import('./pages/admin/UsersManagement'));
const FeedManagement = lazy(() => import('./pages/admin/FeedManagement')); // Updated path
const ProductModerationPage = lazy(() => import('./pages/admin/ProductModerationPage'));
const MakeAdmin = lazy(() => import('./pages/admin/MakeAdmin')); // Added MakeAdmin import
const SystemDiagnosticsPage = lazy(() => import('./pages/admin/SystemDiagnosticsPage')); // Added SystemDiagnosticsPage import

// // Moved down

// Компонент для автоматической прокрутки вверх при навигации
export const ScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname, location.search]);

  return null;
};

export const router = createBrowserRouter(
  [
    {
      path: '/', // Changed '/*' to '/' for the root layout route
      element: <App />,
      errorElement: <ErrorPage />,
      children: [
        {
          path: '',
          element: <Home />,
          errorElement: <ErrorPage />
        },
        {
          path: 'login',
          element: (
            <ErrorBoundary>
              <LoginPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'register',
          element: (
            <ErrorBoundary>
              <RegisterPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'auth/callback',
          element: (
            <ErrorBoundary>
              <AuthCallbackHandler />
            </ErrorBoundary>
          )
        },
        {
          path: 'forgot-password', // Added route
          element: (
            <ErrorBoundary>
              <ForgotPasswordPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'reset-password/:token', // Added route
          element: (
            <ErrorBoundary>
              <ResetPasswordPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'profile',
          element: (
            <PrivateRoute>
              <ErrorBoundary>
                <AccountPage />
              </ErrorBoundary>
            </PrivateRoute>
          )
        },
        {
          path: 'admin',
          element: (
            <PrivateRoute adminOnly>
              <ErrorBoundary>
                <AdminDashboard />
              </ErrorBoundary>
            </PrivateRoute>
          ),
          children: [
            {
              index: true,
              element: (
                // Assuming AdminDashboard (parent) handles PrivateRoute and ErrorBoundary
                <AdminDashboardContent />
              )
            },
            {
              path: 'products',
              element: <ProductManagement /> // Page for listing products
            },
            {
              path: 'products/edit/:id',
              element: (
                <PrivateRoute adminOnly>
                  <ProductEditPage />
                </PrivateRoute>
              )
            },
            {
              path: 'products/new',
              element: (
                <ErrorBoundary>
                  <NewProduct />
                </ErrorBoundary>
              )
            },
            {
              path: 'categories',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <CategoryManagement />
                </Suspense>
              )
            },
            {
              path: 'categories/new',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <CategoryCreatePage />
                </Suspense>
              )
            },
            {
              path: 'categories/edit/:id',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <CategoryEditPage />
                </Suspense>
              )
            },
            {
              path: 'banners',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <BannerManagement />
                </Suspense>
              )
            },
            {
              path: 'banners/edit/:id',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <BannerEditPage />
                </Suspense>
              )
            },
            {
              path: 'banners/new',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <BannerCreatePage />
                </Suspense>
              )
            },
            {
              path: 'users',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <UsersManagement />
                </Suspense>
              )
            },
            {
              path: 'feeds',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <FeedManagement />
                </Suspense>
              )
            },
            {
              path: 'moderation',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <ProductModerationPage />
                </Suspense>
              )
            },
            {
              path: 'orders', // Existing route
              element: <OrdersManagement />
            },
            {
              path: 'orders/:id', // Existing route
              element: <OrderDetailPage />
            },
            {
              path: 'store', // Changed from store-management and using existing import
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <StoreManagement />
                </Suspense>
              )
            },
            {
              path: 'settings', // Добавляем маршрут для /admin/settings
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <StoreSettingsPage />
                </Suspense>
              )
            },
            {
              path: 'product-parameters', // Existing route
              element: <ProductParametersPage />
            },
            {
              path: 'analytics', // Existing route
              element: <AnalyticsPage />
            },
            {
              path: 'reviews', // Existing route
              element: <ReviewsManagementPage />
            },
            {
              path: 'inventory', // Existing route
              element: <Navigate to="/admin" replace /> // Redirect to admin dashboard
            },
            {
              path: 'make-admin',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <MakeAdmin />
                </Suspense>
              )
            },
            {
              path: 'brands',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <BrandManagement />
                </Suspense>
              )
            },
            {
              path: 'brands/create',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <BrandCreate />
                </Suspense>
              )
            },
            {
              path: 'brands/edit/:id',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <BrandEdit />
                </Suspense>
              )
            },
            {
              path: 'diagnostic',
              element: (
                <ErrorBoundary>
                  <DiagnosticPage />
                </ErrorBoundary>
              )
            },
            {
              path: 'system-diagnostics',
              element: (
                <Suspense fallback={<LoadingSpinner />}>
                  <SystemDiagnosticsPage />
                </Suspense>
              )
            }
            // Note: The PrivateRoute and ErrorBoundary wrappers were removed from individual child elements
            // because the parent /admin route already wraps AdminDashboard (which contains the Outlet)
            // with these. If issues arise, they might need to be re-added per child.
          ]
        },
        {
          path: 'cart',
          element: (
            <ErrorBoundary>
              <CartPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'wishlist',
          element: (
            <PrivateRoute>
              <ErrorBoundary>
                <WishlistPage />
              </ErrorBoundary>
            </PrivateRoute>
          )
        },
        {
          path: 'compare',
          element: (
            <ErrorBoundary>
              <ComparePage />
            </ErrorBoundary>
          )
        },
        {
          path: 'checkout',
          element: (
            <ErrorBoundary>
              <CheckoutPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'order-confirmation',
          element: (
            <ErrorBoundary>
              <OrderConfirmationPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'product/:productId',
          element: (
            <ErrorBoundary>
              <ProductPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'categories',
          element: (
            <ErrorBoundary>
              <CategoryPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'category/:categoryId',
          element: (
            <ErrorBoundary>
              <CategoryPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'contact',
          element: (
            <ErrorBoundary>
              <ContactPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'about',
          element: (
            <ErrorBoundary>
              <AboutPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'search',
          element: (
            <ErrorBoundary
              fallback={
                <div className="container mx-auto p-8">Ошибка при загрузке результатов поиска.</div>
              }
            >
              <SearchPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'seed',
          element:
            process.env.NODE_ENV === 'development' ? (
              <PrivateRoute adminOnly>
                <ErrorBoundary>
                  <SeedPage />
                </ErrorBoundary>
              </PrivateRoute>
            ) : (
              <Navigate to="/" replace />
            )
        },
        {
          path: 'sale',
          element: (
            <ErrorBoundary>
              <SalePage />
            </ErrorBoundary>
          )
        },
        {
          path: 'new-arrivals',
          element: (
            <ErrorBoundary>
              <NewArrivalsPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'bestsellers',
          element: (
            <ErrorBoundary>
              <BestsellersPage />
            </ErrorBoundary>
          )
        },
        {
          path: 'partners-demo',
          element: (
            <ErrorBoundary>
              <PartnersDemo />
            </ErrorBoundary>
          )
        },
        {
          path: 'account',
          element: <AccountPage />
        },
        {
          path: 'orders',
          element: (
            <PrivateRoute>
              <ErrorBoundary>
                <OrdersPage />
              </ErrorBoundary>
            </PrivateRoute>
          )
        },
        {
          path: 'orders/:orderId', // Added new route for user order details
          element: (
            <PrivateRoute>
              <ErrorBoundary>
                <UserOrderDetailPage />
              </ErrorBoundary>
            </PrivateRoute>
          )
        },
        {
          path: 'products',
          element: (
            <ErrorBoundary>
              <AllProductsPage />
            </ErrorBoundary>
          )
        },
        {
          path: '*',
          element: <ErrorPage />
        }
      ]
    }
  ],

  {
    future: {
      v7_startTransition: true,
      v7_relativeSplatPath: true // Включаем флаг для корректной работы относительных путей
    }
  }
); // Moved down
