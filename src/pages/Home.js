import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import BannerSection from '../components/BannerSection';
import ParallaxBanner from '../components/ParallaxBanner';
import FeaturedCategories from '../components/home/<USER>';
import ProductSlider from '../components/home/<USER>';
import BrandsSection from '../components/BrandsSection';
import SEO, { SchemaTemplates } from '../seo';

const Home = () => {
  const { t } = useTranslation();

  // Анимация для секций
  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8 }
    }
  };

  // Добавляем класс для body при монтировании компонента
  useEffect(() => {
    document.body.classList.add('has-top-banner');

    // Удаляем класс при размонтировании
    return () => {
      document.body.classList.remove('has-top-banner');
    };
  }, []);

  // Подготавливаем структурированные данные для SEO
  const structuredData = {
    ...SchemaTemplates.organization,
    '@type': ['Organization', 'WebSite'],
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://yoursite.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  };

  return (
    <div className="home-page">
      <SEO
        title={t('home_page_title', 'Главная страница')}
        description={t(
          'home_meta_description',
          'Лучшие товары с доставкой по выгодным ценам в нашем интернет магазине'
        )}
        keywords={t('home_meta_keywords', 'интернет магазин, товары, доставка, акции, скидки')}
        url="/"
        structuredData={structuredData}
      />

      {/* 1. Главный параллакс-баннер */}
      <ParallaxBanner
        title={t('main_banner_title', 'Откройте для себя новую коллекцию')}
        subtitle={t(
          'main_banner_subtitle',
          'Эксклюзивные товары с непревзойденным качеством и стилем'
        )}
        buttonText={t('shop_now', 'Смотреть коллекцию')}
        buttonLink="/products"
        backgroundImage="/images/banners/main-banner.jpg"
        height="xl"
        textColor="white"
        darkOverlay={true}
        overlayOpacity={0.4}
        parallaxStrength={0.5}
      />

      <div className="container mx-auto px-4">
        {/* 2. Преимущества магазина - новая секция */}
        <motion.section
          className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 py-8 md:py-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={sectionVariants}
        >
          <div className="flex flex-col items-center text-center p-4 md:p-6 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 md:w-16 md:h-16 bg-primary/10 rounded-full flex items-center justify-center mb-3 md:mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 md:h-8 md:w-8 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h3 className="text-lg md:text-xl font-semibold mb-2">Гарантия качества</h3>
            <p className="text-sm md:text-base text-gray-600">
              Мы тщательно отбираем каждый товар в нашем магазине
            </p>
          </div>

          <div className="flex flex-col items-center text-center p-4 md:p-6 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 md:w-16 md:h-16 bg-primary/10 rounded-full flex items-center justify-center mb-3 md:mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 md:h-8 md:w-8 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg md:text-xl font-semibold mb-2">Быстрая доставка</h3>
            <p className="text-sm md:text-base text-gray-600">
              Доставим ваш заказ в кратчайшие сроки
            </p>
          </div>

          <div className="flex flex-col items-center text-center p-4 md:p-6 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 md:w-16 md:h-16 bg-primary/10 rounded-full flex items-center justify-center mb-3 md:mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 md:h-8 md:w-8 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                />
              </svg>
            </div>
            <h3 className="text-lg md:text-xl font-semibold mb-2">Удобная оплата</h3>
            <p className="text-sm md:text-base text-gray-600">
              Множество способов оплаты для вашего удобства
            </p>
          </div>
        </motion.section>

        {/* 3. Блок с категориями - с анимацией */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={sectionVariants}
        >
          <FeaturedCategories maxCategories={6} />
        </motion.div>
      </div>

      {/* 4. Акции (слайдер) - между двумя контейнерами для полноэкранной секции */}
      <div className="bg-gray-50 py-10 md:py-16 my-8 md:my-12">
        <div className="container mx-auto px-4">
          <motion.section
            className="mb-6 md:mb-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={sectionVariants}
          >
            <div className="flex justify-between items-center mb-6 md:mb-8">
              <h2 className="text-2xl md:text-3xl font-bold">
                {t('special_offers', 'Специальные предложения')}
              </h2>
              <Link
                to="/sale"
                className="text-primary hover:text-primary-dark transition-colors flex items-center text-sm md:text-base"
              >
                {t('view_all', 'Смотреть все')}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 md:h-5 md:w-5 ml-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
            <ProductSlider type="sale" slidesToShow={4} slidesToScroll={1} autoplay={true} />
          </motion.section>
        </div>
      </div>

      <div className="container mx-auto px-4">
        {/* 5. Новые поступления (слайдер) */}
        <motion.section
          className="mb-10 md:mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={sectionVariants}
        >
          <div className="flex justify-between items-center mb-6 md:mb-8">
            <h2 className="text-2xl md:text-3xl font-bold">
              {t('new_arrivals', 'Новые поступления')}
            </h2>
            <Link
              to="/new-arrivals"
              className="text-primary hover:text-primary-dark transition-colors flex items-center text-sm md:text-base"
            >
              {t('view_all', 'Смотреть все')}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 md:h-5 md:w-5 ml-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Link>
          </div>
          <ProductSlider type="new" slidesToShow={4} slidesToScroll={1} autoplay={true} />
        </motion.section>
      </div>

      {/* Баннер и бестселлеры в общей обертке без отступов */}
      <div className="banner-bestsellers-section" style={{ marginTop: '50px' }}>
        {/* 6. Второй параллакс-баннер - промо */}
        <div className="bottom-banner-container">
          <ParallaxBanner
            title={t('promo_banner_title', 'Скидки до 50% на летнюю коллекцию')}
            subtitle={t('promo_banner_subtitle', 'Только до конца месяца!')}
            buttonText={t('view_offers', 'Смотреть акции')}
            buttonLink="/sale"
            backgroundImage="/images/banners/main-banner.jpg"
            height="large"
            textColor="white"
            darkOverlay={true}
            overlayOpacity={0.5}
            parallaxStrength={0.3}
          />
        </div>

        {/* 7. Хиты продаж (слайдер) - сразу после баннера */}
        <div className="container mx-auto px-4 bestsellers-direct">
          <motion.section
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={sectionVariants}
            className="mb-10 md:mb-16"
          >
            <div className="flex justify-between items-center mb-6 md:mb-8">
              <h2 className="text-2xl md:text-3xl font-bold">{t('bestsellers', 'Хиты продаж')}</h2>
              <Link
                to="/bestsellers"
                className="text-primary hover:text-primary-dark transition-colors flex items-center text-sm md:text-base"
              >
                {t('view_all', 'Смотреть все')}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 md:h-5 md:w-5 ml-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
            <ProductSlider type="bestseller" slidesToShow={4} slidesToScroll={1} autoplay={true} />
          </motion.section>
        </div>

        {/* 8. Наши бренды (слайдер) */}
        <div className="container mx-auto px-4">
          <motion.section
            className="mb-8 md:mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={sectionVariants}
          >
            <BrandsSection />
          </motion.section>
        </div>
      </div>
    </div>
  );
};

export default Home;
