/* Base styles for the application */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom font settings */
@layer base {
  html {
    font-display: optional;
    -webkit-text-size-adjust: 100%;
  }

  body {
    @apply font-sans text-body min-h-screen;
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Fix iOS safe area issues */
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-first typography */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading font-semibold text-heading;
    margin-top: 0;
  }

  h1 {
    @apply text-2xl md:text-h1 leading-tight;
  }
  h2 {
    @apply text-xl md:text-h2 leading-tight;
  }
  h3 {
    @apply text-lg md:text-h3 leading-tight;
  }

  /* Fix mobile tap highlight */
  * {
    -webkit-tap-highlight-color: transparent;
  }

  /* Improve mobile scrolling */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* Mobile-first layout containers */
@layer components {
  .container {
    @apply px-4 md:px-6 mx-auto;
    max-width: 100%;
  }

  .page-container {
    @apply min-h-screen pt-16 pb-20 px-4 md:px-6;
    /* Account for mobile browser chrome */
    min-height: -webkit-fill-available;
  }

  .content-wrapper {
    @apply w-full max-w-7xl mx-auto;
  }

  /* Mobile navigation spacing */
  .nav-spacer {
    height: env(safe-area-inset-top);
  }

  .bottom-nav-spacer {
    height: calc(env(safe-area-inset-bottom) + 4rem);
  }
}

/* Primary colors */
:root {
  --color-primary: #d4b27d;
  --color-primary-dark: #c5a36e;
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-primary-dark {
  background-color: var(--color-primary-dark);
}

.text-primary {
  color: var(--color-primary);
}

.hover\:bg-primary-dark:hover {
  background-color: var(--color-primary-dark);
}

/* Utility classes */
.page-loading img:not([src^='data:']) {
  transition: none !important;
}

.page-loaded img {
  transition: opacity 0.3s ease-in-out;
}

/* Button components */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:ring-2 focus:ring-offset-2;
    /* Improve touch targets on mobile */
    min-height: 44px;
  }

  .btn-primary {
    @apply bg-primary hover:bg-primary-dark text-white focus:ring-primary/50;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-body focus:ring-gray-200;
  }

  .btn-outline {
    @apply border border-primary bg-transparent hover:bg-primary/10 text-primary focus:ring-primary/30;
  }

  .btn-sm {
    @apply text-sm px-3 py-1.5;
    min-height: 36px;
  }

  .btn-lg {
    @apply text-lg px-6 py-2.5;
    min-height: 52px;
  }
}

/* Admin sidebar styles */
.admin-sidebar {
  transition: width 0.3s;
  /* Fix mobile height */
  height: 100dvh;
}

.admin-sidebar.collapsed .admin-sidebar-title {
  display: none;
}

.admin-sidebar.collapsed .admin-menu li a span {
  display: none;
}

.admin-sidebar.collapsed .admin-menu li a i {
  margin-right: 0;
}

/* Mobile-optimized tooltips */
.admin-sidebar.collapsed .admin-menu li a:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  margin-left: 10px;
  white-space: nowrap;
  z-index: 1000;
  font-size: 14px;
}

/* Admin content responsive margins */
.admin-content {
  transition: margin-left 0.3s;
  min-height: 100dvh;
}

@media (min-width: 768px) {
  .admin-sidebar + .admin-content {
    margin-left: 240px;
  }

  .admin-sidebar.collapsed + .admin-content {
    margin-left: 60px;
  }
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  .admin-sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 40;
  }

  .admin-content {
    margin-left: 0;
    margin-bottom: calc(60px + env(safe-area-inset-bottom));
  }
}

/* Refined positioning for partners slider arrows */
.brands-slider {
  position: relative !important;
  overflow: visible !important;
}
.brands-slider .swiper-wrapper {
  padding: 0 60px !important;
}
.brands-slider .swiper-button-prev,
.brands-slider .swiper-button-next {
  top: 50% !important;
}
.brands-slider .swiper-button-prev {
  left: 0 !important;
  transform: translate(-150%, -50%) !important;
}
.brands-slider .swiper-button-next {
  right: 0 !important;
  transform: translate(150%, -50%) !important;
}

/* Ensure slide items appear above navigation arrows */
.brands-slider .swiper-slide {
  position: relative;
  z-index: 2;
}

/* Place navigation arrows behind slide items */
.brands-slider .swiper-button-prev,
.brands-slider .swiper-button-next {
  z-index: 1 !important;
}

/* Banner slider custom styles */
.slick-list {
  overflow: visible !important;
}
.slick-slider {
  position: relative !important;
}
.slick-prev,
.slick-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  width: 40px;
  height: 40px;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.slick-prev {
  left: -60px;
}
.slick-next {
  right: -60px;
}
.slick-prev:before,
.slick-next:before {
  font-size: 20px;
  color: #ffffff;
  opacity: 1 !important;
}
.slick-dots {
  position: absolute;
  bottom: 15px;
  width: 100%;
  display: flex !important;
  justify-content: center;
  padding: 0;
  margin: 0;
}
.slick-dots li {
  margin: 0 4px;
}
.slick-dots li button:before {
  font-size: 12px;
  color: #ffffff;
  opacity: 0.75;
}
.slick-dots li.slick-active button:before {
  opacity: 1;
}

/* Partners marquee continuous scroll */
.partners-marquee {
  display: flex;
  overflow: hidden;
  animation: scrollPartners 20s linear infinite;
}
.partners-marquee:hover {
  animation-play-state: paused;
}
@keyframes scrollPartners {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

