import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

const ParallaxBanner = ({
  title,
  subtitle,
  buttonText,
  buttonLink,
  backgroundImage = '/images/banners/default-top-banner.jpg',
  height = 'large',
  textColor = 'white',
  darkOverlay = true,
  overlayOpacity = 0.4,
  parallaxStrength = 0.5
}) => {
  const { t } = useTranslation();
  const bannerRef = useRef(null);
  const [scrollY, setScrollY] = useState(0);

  // Определение высоты баннера
  const heightClass = {
    small: 'h-64 md:h-80',
    medium: 'h-80 md:h-96', 
    large: 'h-96 md:h-[500px] lg:h-[600px]',
    xl: 'h-[500px] md:h-[600px] lg:h-[700px]'
  }[height] || 'h-96 md:h-[500px]';

  // Обработчик скролла для параллакс-эффекта с throttling
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          if (bannerRef.current) {
            const rect = bannerRef.current.getBoundingClientRect();
            const scrolled = window.pageYOffset;
            const rate = scrolled * -parallaxStrength;

            // Применяем параллакс только когда баннер виден или близко к видимой области
            if (rect.bottom >= -200 && rect.top <= window.innerHeight + 200) {
              setScrollY(rate);
            }
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    // Проверяем поддержку параллакса на устройстве
    const supportsParallax = !window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (supportsParallax) {
      window.addEventListener('scroll', handleScroll, { passive: true });
    }

    return () => {
      if (supportsParallax) {
        window.removeEventListener('scroll', handleScroll);
      }
    };
  }, [parallaxStrength]);

  // Анимации для контента
  const contentVariants = {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: { 
        duration: 1,
        ease: "easeOut",
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };

  return (
    <div 
      ref={bannerRef}
      className={`parallax-banner relative ${heightClass} overflow-hidden`}
      style={{
        width: '100vw',
        marginLeft: 'calc(-50vw + 50%)',
        marginRight: 'calc(-50vw + 50%)'
      }}
    >
      {/* Параллакс фон */}
      <div
        className="parallax-bg absolute inset-0 w-full h-full bg-cover bg-center"
        style={{
          backgroundImage: `url(${backgroundImage})`,
          transform: `translate3d(0, ${scrollY}px, 0) scale(1.1)`,
          willChange: 'transform',
          backfaceVisibility: 'hidden',
          perspective: '1000px'
        }}
      />

      {/* Затемняющий оверлей */}
      {darkOverlay && (
        <div 
          className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/60"
          style={{ opacity: overlayOpacity }}
        />
      )}

      {/* Декоративные элементы */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 border border-white/20 rounded-full"></div>
        <div className="absolute bottom-20 right-20 w-24 h-24 border border-white/20 rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-white/20 rounded-full"></div>
      </div>

      {/* Контент баннера */}
      <div className="absolute inset-0 flex items-center justify-center z-20">
        <div className="container mx-auto px-6 md:px-12">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={contentVariants}
            className="max-w-4xl mx-auto text-center"
          >
            {title && (
              <motion.h1
                variants={itemVariants}
                className={`text-${textColor} font-bold text-4xl md:text-5xl lg:text-6xl xl:text-7xl mb-6 leading-tight`}
                style={{ 
                  textShadow: '2px 2px 20px rgba(0,0,0,0.8)',
                  fontFamily: 'system-ui, -apple-system, sans-serif'
                }}
              >
                {title}
              </motion.h1>
            )}

            {subtitle && (
              <motion.p
                variants={itemVariants}
                className={`text-${textColor} text-lg md:text-xl lg:text-2xl xl:text-3xl mb-8 max-w-3xl mx-auto leading-relaxed`}
                style={{ 
                  textShadow: '1px 1px 10px rgba(0,0,0,0.7)',
                  fontWeight: '300'
                }}
              >
                {subtitle}
              </motion.p>
            )}

            {buttonText && buttonLink && (
              <motion.div
                variants={itemVariants}
                className="mt-8"
              >
                <Link
                  to={buttonLink}
                  className="group inline-flex items-center px-8 py-4 bg-primary hover:bg-primary-dark text-white font-semibold text-lg rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
                >
                  <span className="mr-2">{buttonText}</span>
                  <svg 
                    className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M9 5l7 7-7 7" 
                    />
                  </svg>
                </Link>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>

      {/* Индикатор скролла */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/80"
      >
        <div className="flex flex-col items-center">
          <span className="text-sm mb-2 font-light">
            {t('scroll_down', 'Прокрутите вниз')}
          </span>
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              className="w-1 h-3 bg-white/70 rounded-full mt-2"
            />
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default ParallaxBanner;
