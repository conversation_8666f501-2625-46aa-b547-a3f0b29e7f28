import React, { useState, useEffect } from 'react';
import { supabase } from '../supabaseClient';
import { useTranslation } from 'react-i18next';

const BrandsSection = () => {
  const { t } = useTranslation();
  const [brands, setBrands] = useState([]);

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const { data, error } = await supabase
          .from('brands')
          .select('*')
          .eq('active', true)
          .order('position');
        if (error) throw error;
        setBrands(data || []);
      } catch (err) {
        console.error('Error fetching brands:', err);
      }
    };
    fetchBrands();
  }, []);

  if (!brands.length) return null;

  // Duplicate brands for seamless scroll
  const marqueeBrands = [...brands, ...brands];

  return (
    <section className="py-16 bg-white overflow-hidden">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-semibold text-center mb-6 relative after:block after:w-24 after:h-1 after:bg-primary after:mx-auto after:mt-4">
          {t('our_partners', 'Наши партнёры')}
        </h2>
        <div className="partners-marquee flex items-center space-x-8 py-4">
          {marqueeBrands.map((brand, idx) => (
            <div key={idx} className="flex-shrink-0">
              <a
                href={brand.website_url || brand.link}
                target="_blank"
                rel="noopener noreferrer"
                className="block bg-white rounded-2xl shadow-lg p-6 flex items-center justify-center h-32 transition-transform duration-300 hover:shadow-xl hover:-translate-y-1"
              >
                <img
                  src={brand.logo_url || brand.image}
                  alt={brand.name}
                  className="max-h-20 object-contain"
                  onError={e => {
                    e.target.onerror = null;
                    e.target.src = 'https://placehold.co/150x150/EEE/31343C?text=Logo';
                  }}
                />
              </a>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BrandsSection;
