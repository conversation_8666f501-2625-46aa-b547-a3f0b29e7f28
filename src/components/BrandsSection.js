import React, { useState, useEffect } from 'react';
import { supabase } from '../supabaseClient';
import { useTranslation } from 'react-i18next';
import PartnersSlider from './ui/PartnersSlider';

const BrandsSection = () => {
  const { t } = useTranslation();
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('brands')
          .select('*')
          .eq('active', true)
          .order('position');

        if (error) throw error;
        setBrands(data || []);
      } catch (err) {
        // Fallback to static data if database fails
        const fallbackBrands = [
          { id: 1, name: 'Bosch', logo_url: '/images/partners/bosch.png', website_url: '#' },
          { id: 2, name: 'Siemens', logo_url: '/images/partners/siemens.png', website_url: '#' },
          { id: 3, name: 'Electrolux', logo_url: '/images/partners/electrolux.png', website_url: '#' },
          { id: 4, name: 'AEG', logo_url: '/images/partners/aeg.png', website_url: '#' },
          { id: 5, name: 'Gorenje', logo_url: '/images/partners/gorenje.png', website_url: '#' },
          { id: 6, name: 'Miele', logo_url: '/images/partners/miele.png', website_url: '#' },
          { id: 7, name: 'Samsung', logo_url: '/images/partners/samsung.png', website_url: '#' },
          { id: 8, name: 'LG', logo_url: '/images/partners/lg.png', website_url: '#' },
        ];
        setBrands(fallbackBrands);
      } finally {
        setLoading(false);
      }
    };
    fetchBrands();
  }, []);

  if (loading) {
    return (
      <section className="py-12 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-3 animate-pulse"></div>
            <div className="w-24 h-1 bg-gray-200 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-xl h-28 animate-pulse"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (!brands.length) return null;

  return (
    <section className="py-12 bg-gradient-to-b from-gray-50 to-white overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-gray-800 mb-3">
            {t('our_partners', 'Наши партнёры')}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary-dark mx-auto mb-4"></div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {t('partners_description', 'Мы работаем с ведущими мировыми брендами, чтобы предложить вам только качественную продукцию')}
          </p>
        </div>

        <PartnersSlider
          partners={brands}
          autoPlay={true}
          autoPlayInterval={4000}
          showNavigation={true}
          showDots={true}
          showAutoPlayIndicator={true}
          slidesPerView={{
            mobile: 2,
            tablet: 3,
            desktop: 4,
            large: 5
          }}
        />
      </div>
    </section>
  );
};

export default BrandsSection;
