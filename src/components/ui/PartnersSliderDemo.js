import React from 'react';
import { useTranslation } from 'react-i18next';
import PartnersSlider from './PartnersSlider';

const PartnersSliderDemo = () => {
  const { t } = useTranslation();

  // Демо данные партнёров
  const demoPartners = [
    { id: 1, name: '<PERSON><PERSON>', logo_url: '/images/partners/bosch.png', website_url: 'https://bosch.com' },
    { id: 2, name: 'Siemens', logo_url: '/images/partners/siemens.png', website_url: 'https://siemens.com' },
    { id: 3, name: 'Electrolux', logo_url: '/images/partners/electrolux.png', website_url: 'https://electrolux.com' },
    { id: 4, name: 'AEG', logo_url: '/images/partners/aeg.png', website_url: 'https://aeg.com' },
    { id: 5, name: '<PERSON><PERSON><PERSON>', logo_url: '/images/partners/gorenje.png', website_url: 'https://gorenje.com' },
    { id: 6, name: '<PERSON><PERSON>', logo_url: '/images/partners/miele.png', website_url: 'https://miele.com' },
    { id: 7, name: 'Samsung', logo_url: 'https://placehold.co/150x80/f3f4f6/6b7280?text=Samsung', website_url: 'https://samsung.com' },
    { id: 8, name: 'LG', logo_url: 'https://placehold.co/150x80/f3f4f6/6b7280?text=LG', website_url: 'https://lg.com' },
    { id: 9, name: 'Whirlpool', logo_url: 'https://placehold.co/150x80/f3f4f6/6b7280?text=Whirlpool', website_url: 'https://whirlpool.com' },
    { id: 10, name: 'Beko', logo_url: 'https://placehold.co/150x80/f3f4f6/6b7280?text=Beko', website_url: 'https://beko.com' },
  ];

  return (
    <div className="space-y-16 py-8">
      {/* Основной слайдер */}
      <section className="bg-gradient-to-b from-gray-50 to-white py-12">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold text-gray-800 mb-3">
              {t('our_partners', 'Наши партнёры')}
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary-dark mx-auto mb-4"></div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {t('partners_description', 'Мы работаем с ведущими мировыми брендами, чтобы предложить вам только качественную продукцию')}
            </p>
          </div>

          <PartnersSlider
            partners={demoPartners}
            autoPlay={true}
            autoPlayInterval={4000}
            showNavigation={true}
            showDots={true}
            showAutoPlayIndicator={false}
            slidesPerView={{
              mobile: 2,
              tablet: 3,
              desktop: 4,
              large: 5
            }}
          />
        </div>
      </section>

      {/* Компактный слайдер без навигации */}
      <section className="bg-white py-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-semibold text-gray-800 mb-2">
              Компактная версия
            </h3>
            <p className="text-gray-600">
              Без навигации и индикаторов, только автопрокрутка
            </p>
          </div>

          <PartnersSlider 
            partners={demoPartners.slice(0, 6)}
            autoPlay={true}
            autoPlayInterval={3000}
            showNavigation={false}
            showDots={false}
            showAutoPlayIndicator={false}
            slidesPerView={{
              mobile: 3,
              tablet: 4,
              desktop: 5,
              large: 6
            }}
          />
        </div>
      </section>

      {/* Слайдер с ручным управлением */}
      <section className="bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-semibold text-gray-800 mb-2">
              Ручное управление
            </h3>
            <p className="text-gray-600">
              Без автопрокрутки, только ручная навигация
            </p>
          </div>

          <PartnersSlider 
            partners={demoPartners}
            autoPlay={false}
            showNavigation={true}
            showDots={true}
            showAutoPlayIndicator={false}
            slidesPerView={{
              mobile: 2,
              tablet: 3,
              desktop: 4,
              large: 4
            }}
          />
        </div>
      </section>

      {/* Мобильная версия */}
      <section className="bg-white py-8 md:hidden">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              Мобильная версия
            </h3>
            <p className="text-gray-600 text-sm">
              Оптимизировано для мобильных устройств
            </p>
          </div>

          <PartnersSlider 
            partners={demoPartners.slice(0, 4)}
            autoPlay={true}
            autoPlayInterval={5000}
            showNavigation={false}
            showDots={true}
            showAutoPlayIndicator={true}
            slidesPerView={{
              mobile: 1,
              tablet: 2,
              desktop: 3,
              large: 4
            }}
          />
        </div>
      </section>

      {/* Информация о настройках */}
      <section className="bg-blue-50 py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
              Настройки слайдера
            </h3>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h4 className="text-lg font-semibold mb-4 text-gray-800">Основные параметры</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li><strong>autoPlay:</strong> включить/выключить автопрокрутку</li>
                  <li><strong>autoPlayInterval:</strong> интервал автопрокрутки (мс)</li>
                  <li><strong>showNavigation:</strong> показать стрелки навигации</li>
                  <li><strong>showDots:</strong> показать точки-индикаторы</li>
                  <li><strong>showAutoPlayIndicator:</strong> показать индикатор автопрокрутки</li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h4 className="text-lg font-semibold mb-4 text-gray-800">Адаптивность</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li><strong>mobile:</strong> слайдов на мобильных (&lt;640px)</li>
                  <li><strong>tablet:</strong> слайдов на планшетах (640-768px)</li>
                  <li><strong>desktop:</strong> слайдов на десктопе (768-1024px)</li>
                  <li><strong>large:</strong> слайдов на больших экранах (&gt;1024px)</li>
                </ul>
              </div>
            </div>

            <div className="mt-8 p-6 bg-white rounded-lg shadow-sm">
              <h4 className="text-lg font-semibold mb-4 text-gray-800">Пример использования</h4>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
{`<PartnersSlider 
  partners={partnersData}
  autoPlay={true}
  autoPlayInterval={4000}
  showNavigation={true}
  showDots={true}
  showAutoPlayIndicator={true}
  slidesPerView={{
    mobile: 2,
    tablet: 3,
    desktop: 4,
    large: 5
  }}
/>`}
              </pre>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PartnersSliderDemo;
