import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

const PartnersSlider = ({ 
  partners = [], 
  autoPlay = true, 
  autoPlayInterval = 3000,
  showNavigation = true,
  showDots = true,
  showAutoPlayIndicator = true,
  className = '',
  slidesPerView = { mobile: 2, tablet: 3, desktop: 4, large: 5 }
}) => {
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);
  const [currentSlidesPerView, setCurrentSlidesPerView] = useState(4);
  const sliderRef = useRef(null);
  const autoPlayRef = useRef(null);

  // Responsive slides per view
  useEffect(() => {
    const updateSlidesPerView = () => {
      if (typeof window === 'undefined') return;
      
      const width = window.innerWidth;
      if (width < 640) {
        setCurrentSlidesPerView(slidesPerView.mobile);
      } else if (width < 768) {
        setCurrentSlidesPerView(slidesPerView.tablet);
      } else if (width < 1024) {
        setCurrentSlidesPerView(slidesPerView.desktop);
      } else {
        setCurrentSlidesPerView(slidesPerView.large);
      }
    };

    updateSlidesPerView();
    window.addEventListener('resize', updateSlidesPerView);
    return () => window.removeEventListener('resize', updateSlidesPerView);
  }, [slidesPerView]);

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying && partners.length > currentSlidesPerView) {
      autoPlayRef.current = setInterval(() => {
        setCurrentSlide(prev => {
          const maxSlides = Math.ceil(partners.length / currentSlidesPerView);
          return (prev + 1) % maxSlides;
        });
      }, autoPlayInterval);
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isAutoPlaying, partners.length, currentSlidesPerView, autoPlayInterval]);

  if (!partners.length) return null;

  const totalSlides = Math.ceil(partners.length / currentSlidesPerView);

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const handleMouseEnter = () => {
    if (autoPlay) setIsAutoPlaying(false);
  };

  const handleMouseLeave = () => {
    if (autoPlay) setIsAutoPlaying(true);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  return (
    <div 
      className={`partners-slider relative ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Slider Container */}
      <div className="slider-container overflow-hidden rounded-xl">
        <div 
          ref={sliderRef}
          className="slider-track flex transition-transform duration-500 ease-in-out"
          style={{ 
            transform: `translateX(-${currentSlide * (100 / totalSlides)}%)`,
            width: `${totalSlides * 100}%`
          }}
        >
          {Array.from({ length: totalSlides }).map((_, slideIndex) => (
            <div 
              key={slideIndex}
              className="flex"
              style={{ width: `${100 / totalSlides}%` }}
            >
              {partners
                .slice(slideIndex * currentSlidesPerView, (slideIndex + 1) * currentSlidesPerView)
                .map((partner) => (
                  <div 
                    key={partner.id}
                    className="partner-slide flex-1 px-3"
                  >
                    <div className="group">
                      <a
                        href={partner.website_url || partner.link || '#'}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="partner-card block bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 p-6 h-28 flex items-center justify-center group-hover:scale-105 border border-gray-100"
                      >
                        <img
                          src={partner.logo_url || partner.image}
                          alt={partner.name}
                          className="partner-logo max-h-16 max-w-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                          onError={e => {
                            e.target.onerror = null;
                            e.target.src = `https://placehold.co/150x80/f3f4f6/6b7280?text=${encodeURIComponent(partner.name)}`;
                          }}
                        />
                      </a>
                    </div>
                  </div>
                ))}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Arrows */}
      {showNavigation && totalSlides > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="nav-button prev absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-primary rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110 z-10"
            aria-label={t('previous_slide', 'Previous slide')}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={nextSlide}
            className="nav-button next absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 hover:text-primary rounded-full p-3 shadow-lg transition-all duration-300 hover:scale-110 z-10"
            aria-label={t('next_slide', 'Next slide')}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}

      {/* Dots Indicator */}
      {showDots && totalSlides > 1 && (
        <div className="dots-container flex justify-center mt-8 space-x-2">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`dot w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide 
                  ? 'bg-primary scale-125' 
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
              aria-label={t('go_to_slide', 'Go to slide {{number}}', { number: index + 1 })}
            />
          ))}
        </div>
      )}

      {/* Auto-play indicator */}
      {showAutoPlayIndicator && autoPlay && (
        <div className="autoplay-indicator flex justify-center mt-4">
          <button
            onClick={toggleAutoPlay}
            className="text-sm text-gray-500 hover:text-primary transition-colors duration-300 flex items-center space-x-2"
          >
            <div className={`autoplay-dot w-2 h-2 rounded-full ${isAutoPlaying ? 'bg-green-500' : 'bg-gray-400'}`}></div>
            <span>
              {isAutoPlaying 
                ? t('auto_play_on', 'Автопрокрутка включена') 
                : t('auto_play_off', 'Автопрокрутка выключена')
              }
            </span>
          </button>
        </div>
      )}
    </div>
  );
};

export default PartnersSlider;
